#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件/文件夹拖拽排序工具
支持拖拽文件和文件夹，自动按数字前缀排序，可重新排列顺序并保存
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import re
from pathlib import Path
import shutil
from tkinterdnd2 import DND_FILES, TkinterDnD


class FileFolderSorter:
    def __init__(self, root):
        self.root = root
        self.root.title("文件/文件夹排序工具")
        self.root.geometry("1000x700")  # 增加窗口宽度以显示完整内容

        # 存储文件/文件夹信息的列表
        self.items = []  # 每个元素: {'path': str, 'original_name': str, 'display_name': str}

        self.setup_ui()
        self.setup_drag_drop()
        self.setup_system_drag_drop()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=3)  # 增加列表区域权重
        main_frame.columnconfigure(0, weight=1)  # 按钮区域权重
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件/文件夹排序工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # 添加文件按钮
        ttk.Button(button_frame, text="添加文件", command=self.add_files).pack(fill=tk.X, pady=2)
        
        # 添加文件夹按钮
        ttk.Button(button_frame, text="添加文件夹", command=self.add_folders).pack(fill=tk.X, pady=2)
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", command=self.clear_list).pack(fill=tk.X, pady=2)

        # 排序选项
        self.remove_prefix_var = tk.BooleanVar(value=True)
        self.prefix_checkbox = ttk.Checkbutton(
            button_frame,
            text="排序时去除开头序号",
            variable=self.remove_prefix_var,
            command=self.on_prefix_option_changed
        )
        self.prefix_checkbox.pack(fill=tk.X, pady=2)

        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # 保存按钮
        ttk.Button(button_frame, text="保存重命名", command=self.save_changes,
                  style="Accent.TButton").pack(fill=tk.X, pady=2)
        
        # 列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示文件列表
        self.tree = ttk.Treeview(list_frame, columns=('new_name',), show='tree headings')
        self.tree.heading('#0', text='原始名称')
        self.tree.heading('new_name', text='新名称')

        # 优化列宽设置
        self.tree.column('#0', width=250, minwidth=200)
        self.tree.column('new_name', width=350, minwidth=300)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 说明文本
        info_frame = ttk.Frame(main_frame)
        info_frame.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N), padx=(10, 0))
        
        info_text = tk.Text(info_frame, width=30, height=20, wrap=tk.WORD)
        info_text.insert(tk.END,
            "使用说明：\n\n"
            "1. 点击'添加文件'或'添加文件夹'按钮选择要排序的项目\n\n"
            "2. 或者直接从文件管理器拖拽文件/文件夹到程序窗口中\n\n"
            "3. 自动排序规则：\n"
            "   - 文件夹在前，文件在后\n"
            "   - 文件夹按名称排序\n"
            "   - 文件按名称排序\n"
            "   - 可选择是否去除开头序号再排序\n\n"
            "4. 在列表中拖拽项目可以重新排序\n\n"
            "5. 添加或重新排序后会自动添加数字前缀（1. 2. 3. ...）\n\n"
            "6. 如果文件名前已有数字前缀（如'1.'、'2-'等），会自动去除后重新编号\n\n"
            "7. 点击'保存重命名'将实际重命名文件/文件夹\n\n"
            "支持的前缀格式：\n"
            "- 1. 2. 3. ...\n"
            "- 1- 2- 3- ...\n"
            "- 01. 02. 03. ...\n"
            "等等"
        )
        info_text.config(state=tk.DISABLED)
        info_text.pack(fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_label = tk.Label(self.root, text="准备就绪",
                                   bg="lightgray", fg="black",
                                   relief="sunken", anchor="w")
        self.status_label.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))

    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.tree.bind('<Button-1>', self.on_click)
        self.tree.bind('<B1-Motion>', self.on_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_drop)
        self.tree.bind('<Motion>', self.on_motion)
        self.tree.bind('<Leave>', self.on_leave)

        self.drag_item = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.drag_active = False
        self.highlight_item = None
        self.drag_threshold = 2  # 拖拽阈值，像素（降低阈值让拖拽更敏感）

    def setup_system_drag_drop(self):
        """设置系统拖拽功能"""
        # 注册整个窗口和树控件为拖拽目标
        self.root.drop_target_register(DND_FILES)
        self.tree.drop_target_register(DND_FILES)

        # 绑定拖拽事件
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)
        self.tree.dnd_bind('<<Drop>>', self.on_system_drop)

    def on_system_drop(self, event):
        """处理从系统拖拽的文件/文件夹"""
        try:
            files = self.root.tk.splitlist(event.data)
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"正在添加 {len(files)} 个项目...", bg="lightblue")

            added_any = False
            for file_path in files:
                if self.add_item(file_path):
                    added_any = True

            # 如果添加了任何项目，自动重新排序
            if added_any:
                self.auto_reorder_items()
                if hasattr(self, 'status_label'):
                    self.status_label.config(text=f"成功添加 {len(files)} 个项目", bg="lightgreen")
            else:
                if hasattr(self, 'status_label'):
                    self.status_label.config(text="没有添加新项目", bg="lightyellow")

            # 2秒后恢复状态栏
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray") if hasattr(self, 'status_label') else None)

        except Exception as e:
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")
        
    def on_click(self, event):
        """鼠标点击事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.drag_active = False


    def on_drag(self, event):
        """拖拽事件"""
        if not self.drag_item:
            return

        # 计算移动距离
        dx = abs(event.x - self.drag_start_x)
        dy = abs(event.y - self.drag_start_y)
        distance = max(dx, dy)  # 使用最大值而不是欧几里得距离，更敏感

        # 检查是否开始拖拽（移动距离超过阈值）
        if not self.drag_active and distance > self.drag_threshold:
            self.drag_active = True
            # 立即显示拖拽状态
            self.tree.selection_set(self.drag_item)
            self.tree.focus(self.drag_item)
            self.tree.item(self.drag_item, tags=('dragging',))
            self.tree.configure(cursor="fleur")
            # 更新窗口标题显示拖拽状态
            drag_name = self.get_item_original_name(self.drag_item)
            self.root.title(f"🔄 正在拖拽: {drag_name}")
            # 创建拖拽指示器
            self.create_drag_indicator(event)

        if self.drag_active:
            # 更新拖拽指示器位置
            self.update_drag_indicator(event)

            # 获取鼠标下的项目
            target_item = self.tree.identify_row(event.y)

            # 清除之前的高亮
            if self.highlight_item and self.highlight_item != self.drag_item:
                try:
                    self.tree.set(self.highlight_item, 'new_name',
                                self.get_item_display_name(self.highlight_item))
                    self.tree.item(self.highlight_item, tags=())
                except tk.TclError:
                    pass  # 项目可能已被删除

            # 高亮目标位置
            if target_item and target_item != self.drag_item:
                try:
                    self.highlight_item = target_item
                    original_name = self.get_item_display_name(target_item)
                    self.tree.set(target_item, 'new_name', f"🎯 插入到这里")
                    self.tree.item(target_item, tags=('drop_target',))
                except tk.TclError:
                    self.highlight_item = None
                # 更新窗口标题显示目标
                drag_name = self.get_item_original_name(self.drag_item)
                target_name = self.get_item_original_name(target_item)
                self.root.title(f"🔄 {drag_name} → {target_name}")
            else:
                self.highlight_item = None
                drag_name = self.get_item_original_name(self.drag_item)
                self.root.title(f"🔄 正在拖拽: {drag_name}")

    def on_drop(self, event):
        """放下事件"""


        if not self.drag_item or not self.drag_active:
            self.cleanup_drag()
            return

        target_item = self.tree.identify_row(event.y)
        if target_item and target_item != self.drag_item:
            # 获取拖拽项目的索引
            drag_index = None
            target_index = None

            for i, item_id in enumerate(self.tree.get_children()):
                if item_id == self.drag_item:
                    drag_index = i
                if item_id == target_item:
                    target_index = i

            if drag_index is not None and target_index is not None:
                # 重新排列items列表
                item = self.items.pop(drag_index)
                self.items.insert(target_index, item)

                # 显示拖拽成功的反馈
                self.show_drag_feedback(f"已将项目移动到位置 {target_index + 1}")

                # 手动更新显示（不调用auto_reorder_items，保持用户的拖拽顺序）
                self.update_display()

        self.cleanup_drag()

    def on_motion(self, event):
        """鼠标移动事件（非拖拽状态）"""
        if not self.drag_active:
            # 鼠标悬停效果
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.configure(cursor="hand2")
            else:
                self.tree.configure(cursor="")

    def on_leave(self, event):
        """鼠标离开事件"""
        if not self.drag_active:
            self.tree.configure(cursor="")

    def cleanup_drag(self):
        """清理拖拽状态"""

        # 清除高亮
        if self.highlight_item:
            try:
                self.tree.set(self.highlight_item, 'new_name',
                            self.get_item_display_name(self.highlight_item))
                self.tree.item(self.highlight_item, tags=())
            except tk.TclError:
                pass

        # 清除拖拽项目的样式
        if self.drag_item:
            try:
                self.tree.item(self.drag_item, tags=())
            except tk.TclError:
                pass

        # 重置状态栏
        if hasattr(self, 'status_label'):
            self.status_label.config(text="准备就绪", bg="lightgray")

        self.drag_item = None
        self.drag_active = False
        self.highlight_item = None
        self.tree.configure(cursor="")
        # 恢复窗口标题
        self.root.title("文件夹排序工具")

    def get_item_display_name(self, tree_item_id):
        """获取项目的显示名称"""
        for i, item_id in enumerate(self.tree.get_children()):
            if item_id == tree_item_id:
                return self.items[i]['display_name']
        return ""

    def get_item_original_name(self, tree_item_id):
        """获取项目的原始名称"""
        for i, item_id in enumerate(self.tree.get_children()):
            if item_id == tree_item_id:
                return self.items[i]['original_name']
        return ""

    def show_drag_feedback(self, message):
        """显示拖拽反馈信息"""
        # 创建临时标签显示反馈
        feedback_label = tk.Label(self.root, text=message,
                                bg="lightgreen", fg="black",
                                font=("Arial", 10, "bold"))
        feedback_label.place(relx=0.5, rely=0.1, anchor="center")

        # 2秒后自动消失
        self.root.after(2000, feedback_label.destroy)

    def create_drag_indicator(self, event):
        """创建拖拽指示器"""
        try:
            if hasattr(self, 'drag_indicator'):
                self.drag_indicator.destroy()

            drag_name = self.get_item_original_name(self.drag_item)
            # 简化指示器，放在状态栏位置
            if hasattr(self, 'status_label'):
                self.status_label.config(
                    text=f"🔄 正在拖拽: {drag_name}",
                    bg="lightblue",
                    fg="black"
                )
        except:
            pass

    def update_drag_indicator(self, event):
        """更新拖拽指示器位置"""
        # 简化实现，只更新状态栏
        pass

    def on_prefix_option_changed(self):
        """排序选项改变时的回调"""
        if self.items:
            self.auto_reorder_items()
        
    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(title="选择文件")
        for file_path in files:
            self.add_item(file_path)
        # 自动重新排序
        if files:
            self.auto_reorder_items()

    def add_folders(self):
        """添加文件夹"""
        folder = filedialog.askdirectory(title="选择文件夹")
        if folder:
            self.add_item(folder)
            # 自动重新排序
            self.auto_reorder_items()
            
    def add_item(self, path):
        """添加项目到列表"""
        path_obj = Path(path)
        original_name = path_obj.name

        # 检查是否已存在
        for item in self.items:
            if item['path'] == path:
                return False

        item = {
            'path': path,
            'original_name': original_name,
            'display_name': original_name,
            'is_folder': path_obj.is_dir()
        }

        self.items.append(item)
        return True
        
    def clear_list(self):
        """清空列表"""
        self.items.clear()
        self.refresh_tree()

    def remove_number_prefix(self, name):
        """去除数字前缀"""
        # 匹配各种数字前缀格式：1. 2. 或 1- 2- 或 01. 02. 等
        pattern = r'^(\d+[\.\-]\s*)'
        return re.sub(pattern, '', name).strip()

    def sort_items(self):
        """按照文件夹在前、文件在后的规则排序，各自按名称排序"""
        # 分离文件夹和文件
        folders = [item for item in self.items if item['is_folder']]
        files = [item for item in self.items if not item['is_folder']]

        # 根据选项决定排序键
        if self.remove_prefix_var.get():
            # 去除序号后排序
            folders.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
            files.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
        else:
            # 直接按原始名称排序
            folders.sort(key=lambda x: x['original_name'].lower())
            files.sort(key=lambda x: x['original_name'].lower())

        # 合并：文件夹在前，文件在后
        self.items = folders + files

    def auto_reorder_items(self):
        """自动重新排序并添加数字前缀"""
        # 先排序
        self.sort_items()

        # 然后添加数字前缀
        for i, item in enumerate(self.items, 1):
            # 去除原有的数字前缀
            clean_name = self.remove_number_prefix(item['original_name'])
            # 添加新的数字前缀
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def update_display(self):
        """更新显示（不重新排序，保持当前顺序）"""
        # 只添加数字前缀，不排序
        for i, item in enumerate(self.items, 1):
            # 去除原有的数字前缀
            clean_name = self.remove_number_prefix(item['original_name'])
            # 添加新的数字前缀
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def refresh_tree(self):
        """刷新树形视图"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 添加所有项目
        for item in self.items:
            # 添加文件夹或文件的图标标识
            if item['is_folder']:
                display_text = f"📁 {item['original_name']}"
            else:
                display_text = f"📄 {item['original_name']}"

            self.tree.insert('', tk.END,
                           text=display_text,
                           values=(item['display_name'],))
                           
    def save_changes(self):
        """保存更改，实际重命名文件/文件夹"""
        if not self.items:
            messagebox.showwarning("警告", "没有要保存的项目")
            return
            
        # 确认对话框
        result = messagebox.askyesno("确认", 
            f"确定要重命名 {len(self.items)} 个项目吗？\n\n"
            "注意：此操作不可撤销！")
            
        if not result:
            return
            
        success_count = 0
        error_count = 0
        errors = []
        
        for item in self.items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['display_name']
                
                if old_path != new_path:
                    # 检查目标路径是否已存在
                    if new_path.exists():
                        errors.append(f"目标已存在: {new_path}")
                        error_count += 1
                        continue
                        
                    # 重命名
                    old_path.rename(new_path)
                    
                    # 更新item中的路径信息
                    item['path'] = str(new_path)
                    item['original_name'] = item['display_name']
                    
                success_count += 1
                
            except Exception as e:
                errors.append(f"{item['original_name']}: {str(e)}")
                error_count += 1
                
        # 刷新显示
        self.refresh_tree()
        
        # 显示结果
        message = f"重命名完成！\n成功: {success_count} 个\n失败: {error_count} 个"
        if errors:
            message += f"\n\n错误详情:\n" + "\n".join(errors[:5])
            if len(errors) > 5:
                message += f"\n... 还有 {len(errors) - 5} 个错误"

        if error_count > 0:
            messagebox.showwarning("部分成功", message)
        else:
            messagebox.showinfo("成功", message)

        # 保存成功后自动清空列表
        if success_count > 0:
            self.clear_list()


def main():
    root = TkinterDnD.Tk()
    app = FileFolderSorter(root)
    root.mainloop()


if __name__ == "__main__":
    main()
