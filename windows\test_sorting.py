#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试排序功能
"""

import re

def remove_number_prefix(filename):
    """去除文件名开头的数字前缀"""
    patterns = [
        r'^\d+\.\s*',      # 1. 2. 3.
        r'^\d+\-\s*',      # 1- 2- 3-
        r'^\d+\)\s*',      # 1) 2) 3)
        r'^\d+\s+',        # 1 2 3
        r'^\(\d+\)\s*',    # (1) (2) (3)
        r'^\[\d+\]\s*',    # [1] [2] [3]
    ]
    
    for pattern in patterns:
        filename = re.sub(pattern, '', filename)
    
    return filename

# 测试数据
test_files = [
    "3. 文件C.txt",
    "1. 文件A.txt", 
    "2. 文件B.txt",
    "10. 文件J.txt",
    "文件D.txt",
    "5- 文件E.txt"
]

test_folders = [
    "2. 文件夹B",
    "1. 文件夹A",
    "文件夹C",
    "3- 文件夹D"
]

print("原始文件列表:")
for f in test_files:
    print(f"  {f}")

print("\n原始文件夹列表:")
for f in test_folders:
    print(f"  {f}")

print("\n=== 去除序号后排序 ===")
files_clean = sorted(test_files, key=lambda x: remove_number_prefix(x).lower())
folders_clean = sorted(test_folders, key=lambda x: remove_number_prefix(x).lower())

print("文件夹（去除序号排序）:")
for f in folders_clean:
    clean_name = remove_number_prefix(f)
    print(f"  {f} -> {clean_name}")

print("\n文件（去除序号排序）:")
for f in files_clean:
    clean_name = remove_number_prefix(f)
    print(f"  {f} -> {clean_name}")

print("\n=== 直接按原名排序 ===")
files_direct = sorted(test_files, key=lambda x: x.lower())
folders_direct = sorted(test_folders, key=lambda x: x.lower())

print("文件夹（直接排序）:")
for f in folders_direct:
    print(f"  {f}")

print("\n文件（直接排序）:")
for f in files_direct:
    print(f"  {f}")
