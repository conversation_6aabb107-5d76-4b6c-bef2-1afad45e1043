#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的拖拽测试程序
"""

import tkinter as tk
from tkinter import ttk

class SimpleDragTest:
    def __init__(self, root):
        self.root = root
        self.root.title("拖拽测试")
        self.root.geometry("600x400")
        
        # 创建测试数据
        self.items = [
            "📁 文件夹1",
            "📁 文件夹2", 
            "📄 文件1.txt",
            "📄 文件2.txt"
        ]
        
        self.setup_ui()
        self.setup_drag()
        
    def setup_ui(self):
        frame = ttk.Frame(self.root, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建列表
        self.tree = ttk.Treeview(frame, columns=('new_name',), show='tree headings')
        self.tree.heading('#0', text='原始名称')
        self.tree.heading('new_name', text='新名称')
        
        self.tree.column('#0', width=200)
        self.tree.column('new_name', width=200)
        
        # 配置样式
        self.tree.tag_configure('dragging', background='lightblue')
        self.tree.tag_configure('drop_target', background='lightgreen')
        
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 填充数据
        for i, item in enumerate(self.items):
            self.tree.insert('', tk.END, text=item, values=(f"{i+1}. {item}",))
            
        # 状态标签
        self.status_label = tk.Label(self.root, text="准备就绪", bg="white")
        self.status_label.pack(fill=tk.X)
        
    def setup_drag(self):
        self.tree.bind('<Button-1>', self.on_click)
        self.tree.bind('<B1-Motion>', self.on_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_drop)
        
        self.drag_item = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.drag_active = False
        self.highlight_item = None
        
    def on_click(self, event):
        item = self.tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.drag_active = False
            self.status_label.config(text=f"点击: {item}")
            
    def on_drag(self, event):
        if self.drag_item:
            dx = abs(event.x - self.drag_start_x)
            dy = abs(event.y - self.drag_start_y)
            distance = (dx * dx + dy * dy) ** 0.5
            
            if not self.drag_active and distance > 5:
                self.drag_active = True
                self.tree.selection_set(self.drag_item)
                self.tree.set(self.drag_item, tags=('dragging',))
                self.tree.configure(cursor="fleur")
                self.status_label.config(text=f"开始拖拽: {self.drag_item}")
                
            if self.drag_active:
                target_item = self.tree.identify_row(event.y)
                
                # 清除之前的高亮
                if self.highlight_item and self.highlight_item != self.drag_item:
                    self.tree.set(self.highlight_item, tags=())
                
                # 高亮目标位置
                if target_item and target_item != self.drag_item:
                    self.highlight_item = target_item
                    self.tree.set(target_item, tags=('drop_target',))
                    self.status_label.config(text=f"拖拽到: {target_item}")
                else:
                    self.highlight_item = None
                    
    def on_drop(self, event):
        if not self.drag_item or not self.drag_active:
            self.cleanup_drag()
            return
            
        target_item = self.tree.identify_row(event.y)
        if target_item and target_item != self.drag_item:
            self.status_label.config(text=f"放下: {self.drag_item} -> {target_item}")
            
            # 这里可以添加实际的移动逻辑
            # 为了测试，我们只显示消息
            
        self.cleanup_drag()
        
    def cleanup_drag(self):
        if self.highlight_item:
            self.tree.set(self.highlight_item, tags=())
            
        if self.drag_item:
            self.tree.set(self.drag_item, tags=())
            
        self.drag_item = None
        self.drag_active = False
        self.highlight_item = None
        self.tree.configure(cursor="")
        self.status_label.config(text="拖拽完成")

def main():
    root = tk.Tk()
    app = SimpleDragTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
