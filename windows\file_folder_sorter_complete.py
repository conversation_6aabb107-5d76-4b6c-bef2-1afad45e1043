#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件/文件夹拖拽排序工具 - 完整版本
支持拖拽文件和文件夹，自动按数字前缀排序，可重新排列顺序并保存
新增批量重命名功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import re
from pathlib import Path
import shutil
from tkinterdnd2 import DND_FILES, TkinterDnD

class FileFolderSorterComplete:
    def __init__(self, root):
        self.root = root
        self.root.title("文件/文件夹排序工具")
        self.root.geometry("800x600")  # 减小窗口宽度
        self.root.attributes('-topmost', True)  # 设置窗口置顶

        # 存储文件/文件夹信息的列表
        self.items = []  # 拖拽排序用
        self.batch_items = []  # 批量重命名用

        # 拖拽相关变量
        self.drag_item = None
        self.drag_active = False
        self.highlight_item = None
        self.drag_threshold = 2

        self.setup_ui()
        self.setup_system_drag_drop()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建标签页控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建第一个标签页：拖拽排序
        self.setup_drag_sort_tab()
        
        # 创建第二个标签页：批量重命名
        self.setup_batch_rename_tab()
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="准备就绪", 
                                   bg="lightgray", fg="black", 
                                   relief="sunken", anchor="w")
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)

    def setup_drag_sort_tab(self):
        """设置拖拽排序标签页"""
        # 创建标签页框架
        self.drag_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.drag_frame, text="拖拽排序")
        
        # 主框架
        main_frame = ttk.Frame(self.drag_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置主框架的行列权重
        main_frame.columnconfigure(1, weight=3)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="文件/文件夹排序工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # 添加文件按钮
        ttk.Button(button_frame, text="添加文件", command=self.add_files).pack(fill=tk.X, pady=2)
        
        # 添加文件夹按钮
        ttk.Button(button_frame, text="添加文件夹", command=self.add_folders).pack(fill=tk.X, pady=2)
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", command=self.clear_list).pack(fill=tk.X, pady=2)
        
        # 排序选项
        self.remove_prefix_var = tk.BooleanVar(value=True)
        self.prefix_checkbox = ttk.Checkbutton(
            button_frame, 
            text="排序时去除开头序号", 
            variable=self.remove_prefix_var,
            command=self.on_prefix_option_changed
        )
        self.prefix_checkbox.pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # 保存按钮
        ttk.Button(button_frame, text="保存重命名", command=self.save_changes,
                  style="Accent.TButton").pack(fill=tk.X, pady=2)
        
        # 列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示文件列表
        self.tree = ttk.Treeview(list_frame, columns=('new_name',), show='tree headings')
        self.tree.heading('#0', text='原始名称')
        self.tree.heading('new_name', text='新名称')

        # 优化列宽设置
        self.tree.column('#0', width=250, minwidth=200)
        self.tree.column('new_name', width=350, minwidth=300)
        
        # 配置拖拽样式
        self.tree.tag_configure('dragging', background='lightblue')
        self.tree.tag_configure('drop_target', background='lightgreen')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 设置拖拽事件
        self.tree.bind('<Button-1>', self.on_click)
        self.tree.bind('<B1-Motion>', self.on_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_drop)
        self.tree.bind('<Motion>', self.on_motion)
        self.tree.bind('<Leave>', self.on_leave)

    def setup_batch_rename_tab(self):
        """设置批量重命名标签页"""
        # 创建标签页框架
        self.batch_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_frame, text="批量重命名")
        
        # 主框架
        main_frame = ttk.Frame(self.batch_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量文件重命名工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 左侧按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # 添加文件按钮
        ttk.Button(button_frame, text="添加文件", command=self.batch_add_files).pack(fill=tk.X, pady=2)
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", command=self.batch_clear_list).pack(fill=tk.X, pady=2)
        
        # 排序选项
        self.batch_remove_prefix_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            button_frame, 
            text="排序时去除开头序号", 
            variable=self.batch_remove_prefix_var,
            command=self.batch_sort_files
        ).pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 复制到剪贴板按钮
        ttk.Button(button_frame, text="复制文件名", command=self.copy_filenames).pack(fill=tk.X, pady=2)
        
        # 从剪贴板粘贴按钮
        ttk.Button(button_frame, text="粘贴文件名", command=self.paste_filenames).pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 保存按钮
        ttk.Button(button_frame, text="保存重命名", command=self.batch_save_changes,
                  style="Accent.TButton").pack(fill=tk.X, pady=2)
        
        # 右侧文本编辑区域
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        # 文本编辑器
        self.batch_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))
        
        # 滚动条
        text_scrollbar_v = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.batch_text.yview)
        text_scrollbar_h = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.batch_text.xview)
        self.batch_text.configure(yscrollcommand=text_scrollbar_v.set, xscrollcommand=text_scrollbar_h.set)
        
        # 布局
        self.batch_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        text_scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 说明标签
        info_label = ttk.Label(main_frame, text="拖拽文件到此页面，编辑右侧文件名列表，然后保存", 
                              font=("Arial", 10))
        info_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))

    def remove_number_prefix(self, name):
        """去除数字前缀"""
        pattern = r'^(\d+[\.\-]\s*)'
        return re.sub(pattern, '', name).strip()

    # 拖拽排序功能方法
    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(title="选择要排序的文件")
        if files:
            added_any = False
            for file_path in files:
                if self.add_item(file_path):
                    added_any = True
            if added_any:
                self.auto_reorder_items()

    def add_folders(self):
        """添加文件夹"""
        folder = filedialog.askdirectory(title="选择要排序的文件夹")
        if folder:
            if self.add_item(folder):
                self.auto_reorder_items()

    def add_item(self, path):
        """添加单个项目"""
        path_obj = Path(path)
        if not path_obj.exists():
            return False

        # 检查是否已存在
        for item in self.items:
            if item['path'] == path:
                return False

        # 添加到列表
        self.items.append({
            'path': path,
            'original_name': path_obj.name,
            'display_name': path_obj.name,
            'is_folder': path_obj.is_dir()
        })
        return True

    def clear_list(self):
        """清空列表"""
        self.items.clear()
        for item in self.tree.get_children():
            self.tree.delete(item)

    def on_prefix_option_changed(self):
        """前缀选项改变时重新排序"""
        if self.items:
            self.auto_reorder_items()

    def sort_items(self):
        """按照文件夹在前、文件在后的规则排序，各自按名称排序"""
        folders = [item for item in self.items if item['is_folder']]
        files = [item for item in self.items if not item['is_folder']]

        if self.remove_prefix_var.get():
            folders.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
            files.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
        else:
            folders.sort(key=lambda x: x['original_name'].lower())
            files.sort(key=lambda x: x['original_name'].lower())

        self.items = folders + files

    def auto_reorder_items(self):
        """自动重新排序并添加数字前缀"""
        self.sort_items()

        for i, item in enumerate(self.items, 1):
            clean_name = self.remove_number_prefix(item['original_name'])
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def update_display(self):
        """更新显示（不重新排序，保持当前顺序）"""
        for i, item in enumerate(self.items, 1):
            clean_name = self.remove_number_prefix(item['original_name'])
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def refresh_tree(self):
        """刷新树形视图"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        for item in self.items:
            icon = "📁" if item['is_folder'] else "📄"
            item_id = self.tree.insert('', 'end', text=f"{icon} {item['original_name']}",
                                     values=(item['display_name'],))

    # 拖拽事件处理
    def on_click(self, event):
        """鼠标点击事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.drag_active = False

    def on_drag(self, event):
        """拖拽事件"""
        if not self.drag_item:
            return

        # 计算拖拽距离
        distance = ((event.x - self.drag_start_x) ** 2 + (event.y - self.drag_start_y) ** 2) ** 0.5

        if distance > self.drag_threshold and not self.drag_active:
            self.drag_active = True
            try:
                self.tree.item(self.drag_item, tags=('dragging',))
            except tk.TclError:
                pass
            self.tree.configure(cursor="fleur")

        if self.drag_active:
            target_item = self.tree.identify_row(event.y)

            # 清除之前的高亮
            if self.highlight_item and self.highlight_item != self.drag_item:
                try:
                    self.tree.set(self.highlight_item, 'new_name',
                                self.get_item_display_name(self.highlight_item))
                    self.tree.item(self.highlight_item, tags=())
                except tk.TclError:
                    pass

            # 高亮目标位置
            if target_item and target_item != self.drag_item:
                try:
                    self.highlight_item = target_item
                    self.tree.set(target_item, 'new_name', f"🎯 插入到这里")
                    self.tree.item(target_item, tags=('drop_target',))
                except tk.TclError:
                    self.highlight_item = None

    def on_drop(self, event):
        """放下事件"""
        if not self.drag_item or not self.drag_active:
            self.cleanup_drag()
            return

        target_item = self.tree.identify_row(event.y)
        if target_item and target_item != self.drag_item:
            # 获取拖拽项目的索引
            drag_index = None
            target_index = None

            for i, item_id in enumerate(self.tree.get_children()):
                if item_id == self.drag_item:
                    drag_index = i
                if item_id == target_item:
                    target_index = i

            if drag_index is not None and target_index is not None:
                # 重新排列items列表
                item = self.items.pop(drag_index)
                self.items.insert(target_index, item)

                # 手动更新显示（不调用auto_reorder_items，保持用户的拖拽顺序）
                self.update_display()

        self.cleanup_drag()

    def on_motion(self, event):
        """鼠标移动事件（非拖拽状态）"""
        if not self.drag_active:
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.configure(cursor="hand2")
            else:
                self.tree.configure(cursor="")

    def on_leave(self, event):
        """鼠标离开事件"""
        if not self.drag_active:
            self.tree.configure(cursor="")

    def cleanup_drag(self):
        """清理拖拽状态"""
        # 清除高亮
        if self.highlight_item:
            try:
                self.tree.set(self.highlight_item, 'new_name',
                            self.get_item_display_name(self.highlight_item))
                self.tree.item(self.highlight_item, tags=())
            except tk.TclError:
                pass

        # 清除拖拽项目的样式
        if self.drag_item:
            try:
                self.tree.item(self.drag_item, tags=())
            except tk.TclError:
                pass

        # 重置状态栏
        if hasattr(self, 'status_label'):
            self.status_label.config(text="准备就绪", bg="lightgray")

        self.drag_item = None
        self.drag_active = False
        self.highlight_item = None
        self.tree.configure(cursor="")
        self.root.title("文件/文件夹排序工具")

    def get_item_display_name(self, item_id):
        """获取项目的显示名称"""
        for i, tree_item in enumerate(self.tree.get_children()):
            if tree_item == item_id and i < len(self.items):
                return self.items[i]['display_name']
        return ""

    def save_changes(self):
        """保存重命名更改"""
        if not self.items:
            messagebox.showwarning("警告", "没有文件需要重命名")
            return

        success_count = 0
        error_count = 0

        for item in self.items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['display_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {item['original_name']} -> {item['display_name']}, 错误: {e}")

        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件/文件夹")
            self.clear_list()
        else:
            messagebox.showwarning("部分成功", f"成功重命名 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")

    # 批量重命名功能方法
    def batch_add_files(self):
        """添加文件到批量重命名列表"""
        files = filedialog.askopenfilenames(title="选择要重命名的文件")
        if files:
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    self.batch_items.append({
                        'path': file_path,
                        'original_name': path_obj.name,
                        'new_name': path_obj.name
                    })
            self.batch_sort_files()
            self.update_batch_text()

    def batch_clear_list(self):
        """清空批量重命名列表"""
        self.batch_items.clear()
        self.batch_text.delete(1.0, tk.END)

    def batch_sort_files(self):
        """排序批量重命名的文件"""
        if self.batch_remove_prefix_var.get():
            self.batch_items.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
        else:
            self.batch_items.sort(key=lambda x: x['original_name'].lower())

        # 添加序号
        for i, item in enumerate(self.batch_items, 1):
            clean_name = self.remove_number_prefix(item['original_name'])
            item['new_name'] = f"{i}. {clean_name}"

        self.update_batch_text()

    def update_batch_text(self):
        """更新文本编辑器内容"""
        self.batch_text.delete(1.0, tk.END)
        for item in self.batch_items:
            self.batch_text.insert(tk.END, item['new_name'] + '\n')

    def copy_filenames(self):
        """复制文件名到剪贴板"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件可复制")
            return

        filenames = '\n'.join(item['new_name'] for item in self.batch_items)
        self.root.clipboard_clear()
        self.root.clipboard_append(filenames)
        messagebox.showinfo("成功", f"已复制 {len(self.batch_items)} 个文件名到剪贴板")

    def paste_filenames(self):
        """从剪贴板粘贴文件名"""
        try:
            clipboard_content = self.root.clipboard_get()
            lines = [line.strip() for line in clipboard_content.split('\n') if line.strip()]

            if len(lines) != len(self.batch_items):
                messagebox.showerror("错误", f"文件名数量不匹配！\n当前文件数：{len(self.batch_items)}\n粘贴的文件名数：{len(lines)}")
                return

            # 更新文件名
            for i, line in enumerate(lines):
                if i < len(self.batch_items):
                    self.batch_items[i]['new_name'] = line

            self.update_batch_text()
            messagebox.showinfo("成功", f"已更新 {len(lines)} 个文件名")

        except tk.TclError:
            messagebox.showerror("错误", "剪贴板为空或无法读取")

    def batch_save_changes(self):
        """保存批量重命名更改"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件需要重命名")
            return

        # 获取文本编辑器中的内容
        text_content = self.batch_text.get(1.0, tk.END).strip()
        lines = [line.strip() for line in text_content.split('\n') if line.strip()]

        if len(lines) != len(self.batch_items):
            messagebox.showerror("错误", f"文件名数量不匹配！\n当前文件数：{len(self.batch_items)}\n编辑器中的文件名数：{len(lines)}")
            return

        # 更新文件名
        for i, line in enumerate(lines):
            if i < len(self.batch_items):
                self.batch_items[i]['new_name'] = line

        # 执行重命名
        success_count = 0
        error_count = 0

        for item in self.batch_items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['new_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {item['original_name']} -> {item['new_name']}, 错误: {e}")

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件")
            self.batch_clear_list()
        else:
            messagebox.showwarning("部分成功", f"成功重命名 {success_count} 个文件\n失败 {error_count} 个文件")

    def setup_system_drag_drop(self):
        """设置系统拖拽功能"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)

    def on_system_drop(self, event):
        """处理从系统拖拽的文件/文件夹"""
        try:
            files = self.root.tk.splitlist(event.data)
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "批量重命名":
                # 添加到批量重命名列表
                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists() and path_obj.is_file():
                        self.batch_items.append({
                            'path': file_path,
                            'original_name': path_obj.name,
                            'new_name': path_obj.name
                        })
                self.batch_sort_files()
                self.update_batch_text()
                self.status_label.config(text=f"已添加 {len(files)} 个文件到批量重命名", bg="lightgreen")
            else:
                # 拖拽排序功能
                added_any = False
                for file_path in files:
                    if self.add_item(file_path):
                        added_any = True
                if added_any:
                    self.auto_reorder_items()
                    self.status_label.config(text=f"已添加 {len(files)} 个项目", bg="lightgreen")

            # 2秒后恢复状态栏
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

        except Exception as e:
            self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")

def main():
    root = TkinterDnD.Tk()
    app = FileFolderSorterComplete(root)
    root.mainloop()

if __name__ == "__main__":
    main()
